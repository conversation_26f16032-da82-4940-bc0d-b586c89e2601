import {
  Navbar as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Nav<PERSON><PERSON>ontent,
  Nav<PERSON><PERSON><PERSON>,
} from "@heroui/navbar";
import { Tabs, Tab } from "@heroui/tabs";
import NextLink from "next/link";
import { useState } from "react";

import { siteConfig } from "@/config/site";
import { ThemeSwitch } from "@/components/theme-switch";
import {
  GalleryIcon,
  MusicIcon,
  VideoIcon,
  Logo,
} from "@/components/icons";

export const Navbar = () => {
  const [selectedTab, setSelectedTab] = useState("photos");

  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case "GalleryIcon":
        return <GalleryIcon />;
      case "MusicIcon":
        return <MusicIcon />;
      case "VideoIcon":
        return <VideoIcon />;
      default:
        return null;
    }
  };

  return (
    <HeroUINavbar maxWidth="xl" position="sticky">
      {/* 品牌区域 */}
      <NavbarContent className="basis-1/3" justify="start">
        <NavbarBrand as="li" className="gap-3 max-w-fit">
          <NextLink className="flex justify-start items-center gap-1" href="/">
            <Logo />
            <p className="font-bold text-inherit">Next Touch</p>
          </NextLink>
        </NavbarBrand>
      </NavbarContent>

      {/* 中心Tabs区域 */}
      <NavbarContent className="basis-1/3" justify="center">
        <Tabs
          aria-label="Navigation tabs"
          color="primary"
          variant="bordered"
          selectedKey={selectedTab}
          onSelectionChange={(key) => setSelectedTab(key as string)}
        >
          {siteConfig.tabItems.map((item) => (
            <Tab
              key={item.key}
              title={
                <div className="flex items-center space-x-2">
                  {getIconComponent(item.icon)}
                  <span>{item.label}</span>
                </div>
              }
            />
          ))}
        </Tabs>
      </NavbarContent>

      {/* 主题切换区域 */}
      <NavbarContent className="basis-1/3" justify="end">
        <ThemeSwitch />
      </NavbarContent>
    </HeroUINavbar>
  );
};
